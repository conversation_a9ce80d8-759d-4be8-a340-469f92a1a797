# RS485 Protocol Core Design - Concise Overview

## 1. Executive Summary

**Purpose**: Windows RS485 driver for AI-SLDAP device communication using ZES protocol
**Architecture**: Windows User-Mode Driver Framework (UMDF 2) filter driver with integrated FTDI VCP driver
**Deliverable**: Single executable application (.exe) with complete RS485 communication solution
**Target**: 32 devices maximum (30 slaves + 1 master) on RS485 bus topology
**Key Enhancement**: Type-safe API with specific result types (ConfigurationResult, RequestResult, ResponseResult) for better error handling and semantic clarity

## 2. System Architecture - Three-Layer Design

### 2.1 Layer Structure

```
┌─────────────────────────────────────────────────────────┐
│                 User Application Layer                  │
│              (Business Logic & UI)                      │
└─────────────────────────────────────────────────────────┘
                            │
                    High-Level API Calls
                            │
┌─────────────────────────────────────────────────────────┐
│                RS485 Driver API Layer                   │
│        (5 API Categories + Management Functions)        │
│   • Error Handle API    • Master Broadcasting API       │
│   • Master Assign API   • Master Request API            │
│   • Slave Response API                                  │
└─────────────────────────────────────────────────────────┘
                            │
                   DeviceIoControl Interface
                            │
┌─────────────────────────────────────────────────────────┐
│            Windows UMDF 2 Driver Layer                  │
│    (Protocol Processing + FTDI VCP Integration)         │
│   • Frame Processing    • Buffer Management             │
│   • CRC Validation     • Address Filtering              │
│   • Function Code Routing                               │
└─────────────────────────────────────────────────────────┘
                            │
                      USB-RS485 Hardware
                            │
┌─────────────────────────────────────────────────────────┐
│                   Physical Layer                        │
│              RS485 Bus + FPGA Slaves                    │
└─────────────────────────────────────────────────────────┘
```

### 2.2 Enhanced Driver Architecture

**Driver-Managed Payload Buffers (stores only 12-byte effective data):**
```cpp
class RS485PayloadBuffer {
private:
    static const size_t PAYLOAD_SIZE = 12;  // Store only effective payload data
    uint8_t* m_buffer;                      // Pure payload data storage area
    size_t m_capacity;                      // Payload slot count (uplink: 5, downlink: 10)

public:
    // Core payload operations - guarantee FIFO order
    bool PushPayload(const uint8_t* payloadData);  // Store 12-byte payload
    bool PopPayload(uint8_t* payloadData);         // Retrieve 12-byte payload

    // Buffer flag checking (key mechanism to prevent overflow)
    bool CheckSpaceAvailable() const { return !IsFull(); }
    bool CheckDataAvailable() const { return !IsEmpty(); }

    // FIFO integrity verification
    bool VerifyFIFOIntegrity() const;
};
```

**Buffer Flag Manager:**
```cpp
class BufferFlagManager {
public:
    // Pre-transmission buffer flag check
    bool CheckUplinkSpaceAvailable();

    // Pre-storage buffer flag check
    bool CheckDownlinkSpaceAvailable();

    // Update flags after buffer operations
    void UpdateBufferFlags(size_t uplinkUsed, size_t downlinkUsed);
};
```

### 2.3 Data Flow Architecture

**Frame Structure (16 bytes total)**:
- Header(1) + ID(1) + **Payload(12)** + CRC(1) + Trailer(1)
- **User API handles only 12-byte payload**: Key(4) + Value(8)
- **Driver handles protocol overhead**: Header, ID, CRC, Trailer

**Buffer Management**:
- **Uplink Buffer**: 5 payload slots × 12 bytes = 60 bytes total (PC to device)
- **Downlink Buffer**: 10 payload slots × 12 bytes = 120 bytes total (device to PC)
- **Total Buffer Capacity**: 180 bytes (pure payload data only)
- **Payload-Centric Design**: Buffers store only meaningful 12-byte payload data (Key + Value), not full 16-byte frames
- **FIFO Processing**: Strict First-In-First-Out ordering with sequence integrity verification
- **Buffer Status Monitoring**: Real-time buffer usage tracking with overflow prediction
- **Overflow Protection**: Configurable policies (discard oldest/newest, trigger error) with frame-by-frame checking

## 3. API Categories and Function Code Mapping

| Function Code | Binary | API Category | Description | Used By |
|:-------------:|:------:|:-------------|:------------|:--------|
| **0b111** | 0x07 | Broadcasting + Assign Data | System config + User config | Master |
| **0b110** | 0x06 | Master Request | Data queries | Master |
| **0b010** | 0x02 | Slave Response | Assign acknowledgments | Slave |
| **0b001** | 0x01 | Slave Response | Data responses | Slave |
| **0b000** | 0x00 | Error Handle | Retry mechanism | Both |

## 4. Implementation Priority and Complete API Reference

### 4.1 FIRST PRIORITY - System Configuration (S-series)

| Command | Description | Value Range | API Call |
|---------|-------------|-------------|----------|
| **S001** | Set RS485 slave address | 1-31 | `configureSystemSettings('S001', 5)` |
| **S002** | Set baud rate | 9600, 19200, 38400, 57600, 115200 | `configureSystemSettings('S002', 115200)` |

**S-series Usage Examples:**
```cpp
// Step 1: Set slave address (broadcast to default address 0x00)
// IMPORTANT: Only one slave should be connected during address assignment
uint8_t targetSlaveAddress = 5;  // The address we want to assign
driver.configureSystemSettings("S001", targetSlaveAddress);  // Set address to 5

// Step 2: Set communication baud rate
driver.configureSystemSettings("S002", 115200);   // Set to 115200 bps

// Step 3: Verify configuration
uint8_t responseData[12];
ResponseResult result = driver.receiveSlaveResponse(responseData, 1000);

// Configuration Verification - What PC Receives:
if (result == ResponseResult::SUCCESS) {
    std::string key = extractKey(responseData);
    uint32_t confirmedValue = extractInteger(responseData);

    if (key == "S001") {
        std::cout << "✓ S001 Configuration SUCCESSFUL - Address confirmed: " << confirmedValue << std::endl;
        // Expected: confirmedValue = targetSlaveAddress (the address we just set)
    } else if (key == "S002") {
        std::cout << "✓ S002 Configuration SUCCESSFUL - Baud rate confirmed: " << confirmedValue << std::endl;
        // Expected: confirmedValue = 115200 (the baud rate we just set)
    }
} else if (result == ResponseResult::TIMEOUT) {
    std::cout << "✗ Configuration FAILED - No response from slave" << std::endl;
    std::cout << "Possible causes:" << std::endl;
    std::cout << "  - Slave device not connected or powered off" << std::endl;
    std::cout << "  - Wrong baud rate (for S002 command)" << std::endl;
    std::cout << "  - Multiple slaves connected (for S001 command)" << std::endl;
    std::cout << "Action: Check hardware connections and retry" << std::endl;
} else if (result == ResponseResult::CRC_ERROR) {
    std::cout << "✗ Configuration FAILED - Data corruption detected" << std::endl;
    std::cout << "Possible causes:" << std::endl;
    std::cout << "  - Electrical interference on RS485 bus" << std::endl;
    std::cout << "  - Poor cable connections or damaged cable" << std::endl;
    std::cout << "Action: Check cable integrity and retry" << std::endl;
} else if (result == ResponseResult::INVALID_ADDRESS) {
    std::cout << "✗ Configuration FAILED - Address conflict or invalid" << std::endl;
    std::cout << "Possible causes:" << std::endl;
    std::cout << "  - Multiple slaves with same address (for S001)" << std::endl;
    std::cout << "  - Address out of valid range (1-31)" << std::endl;
    std::cout << "Action: Ensure only one slave connected for S001, check address range" << std::endl;
}
```

### 4.2 SECOND PRIORITY - User Configuration (U-series)

| Command | Description | Value Range | API Call |
|---------|-------------|-------------|----------|
| **U001** | SEL detection threshold | 40-500 milliampere | `configureUserSettings('U001', 250)` |
| **U002** | SEL maximum amplitude threshold | 1000-2000 milliampere | `configureUserSettings('U002', 1500)` |
| **U003** | SEL detection count before power cycle | 1-5 | `configureUserSettings('U003', 3)` |
| **U004** | Power cycle duration | 200,400,600,800,1000 ms | `configureUserSettings('U004', 600)` |
| **U005** | GPIO input channel control | Channel + Enable flag | `configureUserSettings('U005', channelData)` |
| **U006** | GPIO output channel control | Channel + Enable flag | `configureUserSettings('U006', channelData)` |

**U-series Usage Examples:**
```cpp
// Configure SEL detection parameters
driver.configureUserSettings("U001", 250);    // SEL threshold: 250mA
driver.configureUserSettings("U002", 1500);   // Max amplitude: 1500mA
driver.configureUserSettings("U003", 3);      // 3 detections before power cycle
driver.configureUserSettings("U004", 600);    // 600ms power cycle duration

// Configure GPIO channels - Complete Examples
// Enable GPIO input ch0
driver.configureUserSettings("U005", 0x100000000ULL); // Channel=0, Enable=1
// Enable GPIO input ch1
driver.configureUserSettings("U005", 0x100000001ULL); // Channel=1, Enable=1
// Enable GPIO output ch0
driver.configureUserSettings("U006", 0x100000000ULL); // Channel=0, Enable=1
// Enable GPIO output ch1
driver.configureUserSettings("U006", 0x100000001ULL); // Channel=1, Enable=1

// Disable GPIO channels - Complete Examples
// Disable GPIO input ch0
driver.configureUserSettings("U005", 0x000000000ULL); // Channel=0, Enable=0
// Disable GPIO input ch1
driver.configureUserSettings("U005", 0x000000001ULL); // Channel=1, Enable=0
// Disable GPIO output ch0
driver.configureUserSettings("U006", 0x000000000ULL); // Channel=0, Enable=0
// Disable GPIO output ch1
driver.configureUserSettings("U006", 0x000000001ULL); // Channel=1, Enable=0

// Receive acknowledgment for each configuration
uint8_t ackData[12];
driver.receiveSlaveResponse(ackData, 1000);
```

**GPIO Value Packing for U005/U006:**
```cpp
// Format: Lower 32 bits = Channel ID, Upper 32 bits = Enable Flag
uint64_t gpioValue = (static_cast<uint64_t>(enableFlag) << 32) | channelId;

// Complete Examples for All GPIO Operations:
// Enable GPIO input channel 0:  configureUserSettings('U005', 0x100000000ULL)
// Enable GPIO input channel 1:  configureUserSettings('U005', 0x100000001ULL)
// Disable GPIO input channel 0: configureUserSettings('U005', 0x000000000ULL)
// Disable GPIO input channel 1: configureUserSettings('U005', 0x000000001ULL)

// Enable GPIO output channel 0:  configureUserSettings('U006', 0x100000000ULL)
// Enable GPIO output channel 1:  configureUserSettings('U006', 0x100000001ULL)
// Disable GPIO output channel 0: configureUserSettings('U006', 0x000000000ULL)
// Disable GPIO output channel 1: configureUserSettings('U006', 0x000000001ULL)

// Value Calculation Examples:
// Channel 0, Enable:  (1 << 32) | 0 = 0x100000000ULL
// Channel 1, Enable:  (1 << 32) | 1 = 0x100000001ULL
// Channel 0, Disable: (0 << 32) | 0 = 0x000000000ULL
// Channel 1, Disable: (0 << 32) | 1 = 0x000000001ULL
```

### 4.3 THIRD PRIORITY - Application Data Queries (A-series)

| Command | Description | Response Data | API Call |
|---------|-------------|---------------|----------|
| **A001** | Request SEL event log | JSON structure with event records | `requestData('A001')` |
| **A002** | Request device status | Status flags (16-bit) | `requestData('A002')` |
| **A003** | Request firmware version | Version string | `requestData('A003')` |
| **A004** | Request system statistics | JSON structure with statistics | `requestData('A004')` |
| **A005** | Request current configuration | JSON structure with all current settings | `requestData('A005')` |

**A-series Usage Examples:**
```cpp
// Request data (non-blocking design)
driver.requestData("A001");  // Request SEL event log
driver.requestData("A002");  // Request device status
driver.requestData("A003");  // Request firmware version
driver.requestData("A004");  // Request system statistics
driver.requestData("A005");  // Request current configuration

// Receive responses for each request
uint8_t eventData[12], statusData[12], versionData[12];
driver.receiveSlaveResponse(eventData, 1000);
driver.receiveSlaveResponse(statusData, 1000);
driver.receiveSlaveResponse(versionData, 1000);

// Process received data
std::string eventKey = extractKey(eventData);
if (eventKey == "A001") {
    // Process SEL event log JSON data
    uint64_t jsonDataPointer = extractDouble(eventData);
}
```

### 4.4 FOURTH PRIORITY - Model Data Operations (W-series)

| Command | Description | Parameters | API Call |
|---------|-------------|------------|----------|
| **W001** | Write model data to FRAM | Memory address, data bytes | `modelDataOperation(address, data, true, length)` |
| **W002** | Read model data from FRAM | Memory address, length | `modelDataOperation(address, data, false, length)` |

**W-series Usage Examples:**
```cpp
// Write AI model weights to FRAM
uint8_t weightData[12] = {/* weight data */};
driver.modelDataOperation(0x1000, weightData, true, 12);  // Write to address 0x1000

// Read AI model bias from FRAM
uint8_t biasData[12];
driver.modelDataOperation(0x2000, biasData, false, 12);   // Read from address 0x2000

// Receive acknowledgment/response
uint8_t responseData[12];
driver.receiveSlaveResponse(responseData, 1000);
```

### 4.5 FIFTH PRIORITY - Error Handling and Management APIs

**Management APIs (FTDI-Style):**
```cpp
// Port Management
ConnectionResult openPort(const std::string& portName);
ConnectionResult closePort();
bool isPortOpen() const;
PortResult getPortInfo(PortInfo& info);

// Device Discovery
static EnumerationResult enumerateDevices(std::vector<DeviceInfo>& deviceList);
static DetectionResult detectMultipleDevices(std::vector<uint8_t>& detectedAddresses);

// Buffer Management (Mandatory before data transmission)
BufferResult getBufferStatus(BufferStatus& status);
BufferResult checkUplinkBufferAvailability(bool& isFull);
BufferResult checkDownlinkBufferAvailability(bool& isFull);
BufferResult clearBuffer(BufferType bufferType = BufferType::BOTH);
BufferResult setBufferOverflowPolicy(BufferOverflowPolicy policy);

// BufferStatus structure for detailed buffer monitoring
struct BufferStatus {
    uint32_t uplinkUsed;        // Used payload slots in uplink buffer (0-5)
    uint32_t uplinkTotal;       // Total uplink buffer capacity (5 payload slots)
    uint32_t downlinkUsed;      // Used payload slots in downlink buffer (0-10)
    uint32_t downlinkTotal;     // Total downlink buffer capacity (10 payload slots)
    uint32_t payloadSize;       // Size per payload slot (12 bytes)
    bool isUplinkFull;          // Uplink buffer full flag
    bool isDownlinkFull;        // Downlink buffer full flag
    bool isOverflowDetected;    // Buffer overflow status
    uint32_t totalBufferBytes;  // Total buffer capacity in bytes (180 bytes)
};

// Hardware Status and Performance
HardwareResult getHardwareStatus(HardwareStatus& status);
PerformanceResult getPerformanceMetrics(PerformanceMetrics& metrics);
LineResult getLineStatus(LineStatus& status);
ConfigResult getBaudRate(uint32_t& currentBaudRate);

// Error Handling
std::string getErrorString(const ConnectionResult& error);
std::string getErrorString(const BufferResult& error);
std::string getErrorString(const ConfigurationResult& error);
std::string getErrorString(const RequestResult& error);
std::string getErrorString(const ResponseResult& error);
```

**Specific Result Types for Enhanced Type Safety:**
```cpp
// Different result types for different operation categories - replaces generic RS485Error
enum class ConnectionResult { SUCCESS, PORT_NOT_FOUND, ACCESS_DENIED, ALREADY_OPEN, CONNECTION_ERROR, DEVICE_NOT_FOUND };
enum class BufferResult { SUCCESS, INSUFFICIENT_BUFFER, BUFFER_OVERFLOW, INVALID_BUFFER_TYPE, BUFFER_FULL };
enum class ConfigurationResult { SUCCESS, INVALID_PARAMETER, DEVICE_NOT_RESPONDING, CRC_ERROR, TIMEOUT_ERROR, PROTOCOL_ERROR };
enum class RequestResult { SUCCESS, INVALID_COMMAND, BUFFER_FULL, TIMEOUT_ERROR, DEVICE_BUSY };
enum class ResponseResult { SUCCESS, TIMEOUT_ERROR, CRC_ERROR, INVALID_ADDRESS, NO_DATA_AVAILABLE };
enum class HardwareResult { SUCCESS, HARDWARE_ERROR, DRIVER_ERROR };
enum class PerformanceResult { SUCCESS, METRICS_UNAVAILABLE };
enum class LineResult { SUCCESS, LINE_ERROR };
enum class EnumerationResult { SUCCESS, NO_DEVICES_FOUND, ENUMERATION_ERROR };
enum class DetectionResult { SUCCESS, MULTIPLE_DEVICES, NO_RESPONSE };
enum class ModelDataResult { SUCCESS, MEMORY_ERROR, INVALID_ADDRESS, DATA_CORRUPTION };
enum class VerificationResult { SUCCESS, VALUE_MISMATCH, VERIFICATION_FAILED };
```

**Error Handling Usage Examples:**
```cpp
// Complete error handling workflow with specific result types
ConnectionResult portResult = driver.openPort("COM3");
if (portResult != ConnectionResult::SUCCESS) {
    std::cout << "Port Error: " << driver.getErrorString(portResult) << std::endl;
    return -1;
}

// Buffer status checking before transmission
BufferStatus status;
BufferResult bufferResult = driver.getBufferStatus(status);
if (bufferResult != BufferResult::SUCCESS) {
    std::cout << "Buffer Error: " << driver.getErrorString(bufferResult) << std::endl;
}

// Configuration with error handling
ConfigurationResult configResult = driver.configureSystemSettings("S001", 5);
if (configResult != ConfigurationResult::SUCCESS) {
    std::cout << "Config Error: " << driver.getErrorString(configResult) << std::endl;
}

// Request with error handling
RequestResult requestResult = driver.requestData("A001");
if (requestResult != RequestResult::SUCCESS) {
    std::cout << "Request Error: " << driver.getErrorString(requestResult) << std::endl;
}

// Response handling with timeout and error checking
uint8_t responseData[12];
ResponseResult responseResult = driver.receiveSlaveResponse(responseData, 1000);
if (responseResult == ResponseResult::TIMEOUT_ERROR) {
    std::cout << "Response Timeout: " << driver.getErrorString(responseResult) << std::endl;
} else if (responseResult == ResponseResult::CRC_ERROR) {
    std::cout << "CRC Error: " << driver.getErrorString(responseResult) << std::endl;
    // Automatic retry mechanism will be triggered
}
```

## 5. Response Data Processing

### 5.1 Slave Response API

```cpp
// Slave Response API - Handles both acknowledgments and data
ResponseResult receiveSlaveResponse(uint8_t responseData[12], uint32_t timeoutMs = 1000);

// Advanced response handling with wait options
ResponseResult receiveSlaveResponse(uint8_t responseData[12], bool waitForData, uint32_t timeoutMs);
```

### 5.2 Data Extraction Helper Functions

```cpp
// PayloadDataExtractor class for consistent data handling
class PayloadDataExtractor {
public:
    // Extract key from bytes 0-3 (4-byte ASCII string)
    static std::string extractKey(const uint8_t* responseData);

    // Extract 32-bit integer from bytes 4-7 (little-endian)
    static uint32_t extractInteger(const uint8_t* responseData);

    // Extract IEEE 754 single-precision float from bytes 4-7
    static float extractFloat(const uint8_t* responseData);

    // Extract IEEE 754 double-precision float from bytes 4-11
    static double extractDouble(const uint8_t* responseData);

    // Extract dual 32-bit integers from bytes 4-7 and 8-11
    static std::pair<uint32_t, uint32_t> extractDualIntegers(const uint8_t* responseData);
};

// Convenience functions (same as PayloadDataExtractor methods)
std::string extractKey(const uint8_t* responseData);
uint32_t extractInteger(const uint8_t* responseData);
float extractFloat(const uint8_t* responseData);
double extractDouble(const uint8_t* responseData);
std::pair<uint32_t, uint32_t> extractDualIntegers(const uint8_t* responseData);

// Example usage:
uint8_t responseData[12];
ResponseResult result = driver.receiveSlaveResponse(responseData, 1000);
if (result == ResponseResult::SUCCESS) {
    std::string key = extractKey(responseData);      // Bytes 0-3
    uint32_t value = extractInteger(responseData);   // Bytes 4-7

    if (key == "U001") {
        std::cout << "SEL threshold confirmed: " << value << " mA" << std::endl;
    } else if (key == "A002") {
        std::cout << "Device status: 0x" << std::hex << value << std::endl;
    }
}
```

## 6. Complete Usage Workflow Examples

### 6.1 Phase 1: System Setup (S-series Implementation)

```cpp
// Step 1: Initialize driver and check hardware
RS485Driver driver;
ConnectionResult portResult = driver.openPort("COM3");
if (portResult != ConnectionResult::SUCCESS) {
    std::cout << "Failed to open port: " << driver.getErrorString(portResult) << std::endl;
    return -1;
}

// Step 2: Detect devices (ensure only one slave for address assignment)
std::vector<uint8_t> detectedAddresses;
DetectionResult detection = driver.detectMultipleDevices(detectedAddresses);
if (detectedAddresses.size() > 1) {
    std::cout << "Multiple slaves detected. Please connect only one slave for address assignment." << std::endl;
    return -1;
}

// Step 3: Set slave address (broadcast to default address 0x00)
ConfigurationResult s001Result = driver.configureSystemSettings("S001", 5);
if (s001Result != ConfigurationResult::SUCCESS) {
    std::cout << "S001 Error: " << driver.getErrorString(s001Result) << std::endl;
}

// Step 4: Receive S001 acknowledgment
uint8_t s001Response[12];
uint8_t assignedAddress = 5;  // The address we assigned in Step 3
ResponseResult s001Ack = driver.receiveSlaveResponse(s001Response, 1000);
if (s001Ack == ResponseResult::SUCCESS) {
    std::string key = extractKey(s001Response);
    uint32_t confirmedAddress = extractInteger(s001Response);
    std::cout << "Address assignment confirmed: " << confirmedAddress << std::endl;
}

// Step 5: Set baud rate
ConfigurationResult s002Result = driver.configureSystemSettings("S002", 115200);
uint8_t s002Response[12];
driver.receiveSlaveResponse(s002Response, 1000);
```

### 6.2 Phase 2: User Configuration (U-series Implementation)

```cpp
// Configure SEL detection parameters with acknowledgment checking
struct ConfigCommand {
    std::string key;
    uint64_t value;
    std::string description;
};

std::vector<ConfigCommand> userConfigs = {
    {"U001", 250, "SEL detection threshold: 250mA"},
    {"U002", 1500, "SEL max amplitude: 1500mA"},
    {"U003", 3, "Detection count before power cycle: 3"},
    {"U004", 600, "Power cycle duration: 600ms"},
    {"U005", 0x100000000ULL, "Enable GPIO input channel 0"},
    {"U006", 0x100000001ULL, "Enable GPIO output channel 1"}
};

for (const auto& config : userConfigs) {
    // Send configuration command
    ConfigurationResult result = driver.configureUserSettings(config.key, config.value);
    if (result != ConfigurationResult::SUCCESS) {
        std::cout << "Config Error for " << config.key << ": "
                  << driver.getErrorString(result) << std::endl;
        continue;
    }

    // Receive acknowledgment
    uint8_t ackData[12];
    ResponseResult ackResult = driver.receiveSlaveResponse(ackData, 1000);
    if (ackResult == ResponseResult::SUCCESS) {
        std::string ackKey = extractKey(ackData);
        uint32_t ackValue = extractInteger(ackData);
        std::cout << config.description << " - Confirmed: " << ackValue << std::endl;
    } else {
        std::cout << "ACK Error for " << config.key << ": "
                  << driver.getErrorString(ackResult) << std::endl;
    }
}
```

### 6.3 Phase 3: Data Monitoring (A-series Implementation)

```cpp
// Request various types of data
std::vector<std::string> dataRequests = {"A001", "A002", "A003", "A004", "A005"};

for (const auto& request : dataRequests) {
    // Send data request (non-blocking)
    RequestResult reqResult = driver.requestData(request);
    if (reqResult != RequestResult::SUCCESS) {
        std::cout << "Request Error for " << request << ": "
                  << driver.getErrorString(reqResult) << std::endl;
        continue;
    }

    // Receive response data
    uint8_t responseData[12];
    ResponseResult respResult = driver.receiveSlaveResponse(responseData, 2000);
    if (respResult == ResponseResult::SUCCESS) {
        std::string responseKey = extractKey(responseData);

        if (responseKey == "A001") {
            // SEL event log - JSON data pointer
            uint64_t jsonPointer = extractDouble(responseData);
            std::cout << "SEL Event Log received, JSON pointer: 0x"
                      << std::hex << jsonPointer << std::endl;
        } else if (responseKey == "A002") {
            // Device status - 16-bit flags
            uint32_t statusFlags = extractInteger(responseData);
            std::cout << "Device Status: 0x" << std::hex << statusFlags << std::endl;
        } else if (responseKey == "A003") {
            // Firmware version
            uint32_t version = extractInteger(responseData);
            std::cout << "Firmware Version: " << std::dec << version << std::endl;
        } else if (responseKey == "A004") {
            // System statistics - JSON data pointer
            uint64_t statsPointer = extractDouble(responseData);
            std::cout << "System Statistics received, JSON pointer: 0x"
                      << std::hex << statsPointer << std::endl;
        } else if (responseKey == "A005") {
            // Current configuration - JSON data pointer
            uint64_t configPointer = extractDouble(responseData);
            std::cout << "Current Configuration received, JSON pointer: 0x"
                      << std::hex << configPointer << std::endl;
        }
    } else {
        std::cout << "Response Error for " << request << ": "
                  << driver.getErrorString(respResult) << std::endl;
    }
}
```

### 6.4 Phase 4: Model Data Operations (W-series Implementation)

```cpp
// Write AI model weights to FRAM
uint8_t weightData[12] = {
    'W', '0', '0', '1',  // Key: "W001"
    0x00, 0x00, 0x80, 0x3F,  // Float value: 1.0f in IEEE 754
    0x00, 0x00, 0x00, 0x00   // Padding
};

// Write operation
ConfigurationResult writeResult = driver.modelDataOperation(0x1000, weightData, true, 12);
if (writeResult == ConfigurationResult::SUCCESS) {
    uint8_t writeAck[12];
    ResponseResult writeAckResult = driver.receiveSlaveResponse(writeAck, 1000);
    if (writeAckResult == ResponseResult::SUCCESS) {
        std::cout << "Model weight written successfully to FRAM address 0x1000" << std::endl;
    }
}

// Read AI model bias from FRAM
uint8_t biasData[12];
RequestResult readResult = driver.modelDataOperation(0x2000, biasData, false, 12);
if (readResult == RequestResult::SUCCESS) {
    uint8_t readResponse[12];
    ResponseResult readRespResult = driver.receiveSlaveResponse(readResponse, 1000);
    if (readRespResult == ResponseResult::SUCCESS) {
        std::string key = extractKey(readResponse);
        float biasValue = extractFloat(readResponse);
        std::cout << "Model bias read from FRAM: " << biasValue << std::endl;
    }
}
```

### 6.5 Complete Error Handling and Recovery

```cpp
// Comprehensive error handling with automatic retry
void robustCommunication() {
    const int MAX_RETRIES = 3;

    for (int attempt = 0; attempt < MAX_RETRIES; attempt++) {
        // Check buffer status before transmission
        BufferStatus bufferStatus;
        BufferResult bufferResult = driver.getBufferStatus(bufferStatus);
        if (bufferResult != BufferResult::SUCCESS) {
            std::cout << "Buffer check failed: " << driver.getErrorString(bufferResult) << std::endl;
            driver.clearBuffer(BufferType::BOTH);
            continue;
        }

        if (bufferStatus.isUplinkFull) {
            std::cout << "Uplink buffer full, clearing..." << std::endl;
            driver.clearBuffer(BufferType::UPLINK);
        }

        // Attempt configuration
        ConfigurationResult configResult = driver.configureUserSettings("U001", 250);
        if (configResult == ConfigurationResult::SUCCESS) {
            // Wait for acknowledgment
            uint8_t ackData[12];
            ResponseResult ackResult = driver.receiveSlaveResponse(ackData, 1000);

            if (ackResult == ResponseResult::SUCCESS) {
                std::cout << "Configuration successful on attempt " << (attempt + 1) << std::endl;
                return;  // Success
            } else if (ackResult == ResponseResult::CRC_ERROR) {
                std::cout << "CRC error detected, retrying..." << std::endl;
                continue;  // Retry
            } else if (ackResult == ResponseResult::TIMEOUT) {
                std::cout << "Timeout occurred, retrying..." << std::endl;
                continue;  // Retry
            }
        }
    }

    std::cout << "Communication failed after " << MAX_RETRIES << " attempts" << std::endl;
}
```

## 7. Key Design Benefits

### 7.1 User Simplicity
- **Raw Data Input**: Users provide values directly (250, 1500, 3.14159f)
- **Automatic Conversion**: Driver handles all binary encoding/decoding
- **Cross-Platform**: Same code works on Windows/Linux/ARM
- **Enhanced Type Safety**: Specific result types (ConfigurationResult, RequestResult, ResponseResult) replace generic RS485Error for better semantic clarity
- **Universal Data Format**: IEEE 754 and little-endian standards ensure compatibility
- **Language Independence**: Same format works in C++, Python, Java, C#
- **Payload-Centric Design**: All communication based on 12-byte payload structure (Key + Value)

### 7.2 Protocol Abstraction
- **Hidden Complexity**: Users never see 16-byte frames or protocol details
- **Automatic Routing**: Function codes route to correct API categories
- **Buffer Management**: Automatic buffer checking before transmission
- **Error Recovery**: Intelligent retry mechanism for transient errors

### 7.3 Enterprise Features
- **FTDI Integration**: Built-in USB-RS485 converter support with filter driver architecture
- **Single Executable**: No separate driver installation required
- **Real-Time Performance**: Fixed-size payload buffers (180 bytes total) for deterministic behavior
- **Comprehensive Logging**: Detailed error reporting and diagnostics with specific result types
- **Windows Driver Kit**: UMDF 2 filter driver framework with DeviceIoControl interface
- **Thread-Safe Operations**: All buffer operations protected by synchronization
- **Non-Blocking Design**: Driver operations never block user threads with dedicated thread pools
- **Predictive Buffer Management**: Overflow prevention with frame-by-frame transmission checking

## 8. Data Format Specification

### 8.1 Cross-Platform Data Format
**Universal 12-byte Payload Structure:**
- **Bytes 0-3**: Command Key (4-byte ASCII string, e.g., "S001", "U001", "A001")
- **Bytes 4-11**: Value Data (8-byte binary in little-endian format)

**Data Type Encoding:**
```cpp
// Integer values (32-bit, uses bytes 4-7, bytes 8-11 set to zero)
driver.configureUserSettings("U001", 250);        // 250 mA threshold

// Floating-point values (IEEE 754 standard)
driver.configureUserSettings("W001", 3.14159f);   // Single precision (bytes 4-7)
driver.configureUserSettings("W002", 2.71828);    // Double precision (bytes 4-11)

// Dual integer values (GPIO configuration)
driver.configureUserSettings("U005", 0x100000000ULL);  // Channel=0, Enable=1
```

**Cross-Platform Guarantees:**
- **Byte Order**: All multi-byte values use little-endian format
- **Alignment**: No special alignment requirements
- **Padding**: Unused bytes set to zero
- **IEEE 754**: Standard floating-point representation
- **Type Safety**: API functions handle conversion automatically

## 9. Enhanced Driver Architecture Implementation

**Driver-Managed Payload Buffers (stores only 12-byte effective data):**
```cpp
class RS485PayloadBuffer {
private:
    static const size_t PAYLOAD_SIZE = 12;  // Store only effective payload data
    uint8_t* m_buffer;                      // Pure payload data storage area
    size_t m_capacity;                      // Payload slot count (uplink: 5, downlink: 10)

public:
    // Core payload operations - guarantee FIFO order
    bool PushPayload(const uint8_t* payloadData);  // Store 12-byte payload
    bool PopPayload(uint8_t* payloadData);         // Retrieve 12-byte payload

    // Buffer flag checking (key mechanism to prevent overflow)
    bool CheckSpaceAvailable() const { return !IsFull(); }
    bool CheckDataAvailable() const { return !IsEmpty(); }

    // FIFO integrity verification
    bool VerifyFIFOIntegrity() const;
};
```

**Buffer Flag Manager:**
```cpp
class BufferFlagManager {
public:
    // Pre-transmission buffer flag check
    bool CheckUplinkSpaceAvailable();

    // Pre-storage buffer flag check
    bool CheckDownlinkSpaceAvailable();

    // Update flags after buffer operations
    void UpdateBufferFlags(size_t uplinkUsed, size_t downlinkUsed);
};
```

**Implementation Benefits:**
- **Payload-Centric Design**: Stores only meaningful 12-byte data (Key + Value), eliminating protocol overhead
- **Memory Efficiency**: 180 bytes total buffer capacity (60 bytes uplink + 120 bytes downlink)
- **FIFO Guarantee**: Strict First-In-First-Out ordering with integrity verification
- **Overflow Prevention**: Comprehensive flag checking before every buffer operation
- **Thread Safety**: All buffer operations protected with minimal locking overhead
- **Real-Time Performance**: Fixed-size buffers for deterministic behavior in airborne environments

## 10. Implementation Notes

- **Address Management**: S001 sets target address used internally by the driver for all subsequent commands
  - **Internal Usage**: Driver automatically uses the configured slave address for all U/A-series commands
  - **Single Device Focus**: Driver is designed for dedicated communication with one slave device at a time
  - **Multi-Slave Support**: Change address using S001 before communicating with different slaves
- **Configuration Persistence**: All U-series settings saved to FRAM automatically
- **Buffer Overflow**: Configurable policies (discard oldest/newest, trigger error)
- **Multi-Frame Responses**: Automatic handling for responses >8 bytes
- **Acknowledgment System**: Mandatory acknowledgments for reliability

## 11. API Summary Table

| API Category | Function | Purpose | Command Series | Example |
|--------------|----------|---------|----------------|---------|
| **Error Handle API** | `getErrorString(error)` | Detailed error information | N/A | `driver.getErrorString(result)` |
| **Master Broadcasting API** | `configureSystemSettings(key, value)` | System configuration | S-series | `configureSystemSettings("S001", 5)` |
| **Master Assign Data API** | `configureUserSettings(key, value)` | User configuration | U-series | `configureUserSettings("U001", 250)` |
| **Master Assign Data API** | `modelDataOperation(addr, data, write, len)` | AI model data | W-series | `modelDataOperation(0x1000, data, true, 12)` |
| **Master Request API** | `requestData(key)` | Data queries | A-series | `requestData("A001")` |
| **Slave Response API** | `receiveSlaveResponse(data, timeout)` | Receive responses | N/A | `receiveSlaveResponse(data, 1000)` |

**Key Design Principles:**
- **12-byte payload focus**: All communication based on Key(4) + Value(8) structure with payload-centric buffer management
- **Address consistency**: Use S001-assigned address for all subsequent operations
- **Enhanced type safety**: Specific result types (ConfigurationResult, RequestResult, ResponseResult) replace generic RS485Error for better semantic clarity
- **Cross-platform compatibility**: Universal data format works on Windows/Linux/ARM with IEEE 754 and little-endian standards
- **Enterprise-ready**: Single executable with integrated FTDI filter driver support and UMDF 2 framework
- **Non-blocking architecture**: Dedicated thread pools ensure user threads never block during RS485 operations
- **Predictive buffer management**: Frame-by-frame overflow prevention with intelligent scheduling

